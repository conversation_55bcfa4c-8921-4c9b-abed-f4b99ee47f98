<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40" @click="toggleServerStatus">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view
				class="server-list-item mt-16 p-16"
				:class="{ 'server-offline': mockServerStatus.status === false }"
			>
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<view class="relative">
							<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-80 h-80 mr-20" />
							<view
								v-if="mockServerStatus.status === true"
								class="online-indicator"
							></view>
							<view
								v-else-if="mockServerStatus.status === false"
								class="offline-indicator"
							></view>
							<view
								v-else
								class="loading-indicator"
							></view>
						</view>
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">{{ mockServerStatus.name }}</text>
								<text
									v-if="mockServerStatus.status === true"
									class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24"
								>
									{{ mockServerStatus.onlineTime }}
								</text>
								<text
									v-else-if="mockServerStatus.status === false"
									class="bg-#E7E7E7 text-#fff px-8 py-2 rd-4 ml-10 text-24"
								>
									离线
								</text>
								<text
									v-else
									class="bg-#999 text-#fff px-8 py-2 rd-4 ml-10 text-24"
								>
									连接中
								</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：{{ mockServerStatus.ip }}</text>
						</view>
					</view>
					<view
						class="flex items-center justify-between rd-16 px-8 py-4"
						style="border: 3rpx solid rgba(0, 0, 0, 0.1)"
					>
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-50 h-50 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20 text-#A7A7A7">88kb/s</text>
							<text class="text-20 mt-10 text-#A7A7A7">10kb/s</text>
						</view>
					</view>
				</view>
				<!-- 服务器监控指标网格 -->
				<view class="metrics-grid">
					<!-- 负载指标 - 第1列，跨2行 -->
					<view class="load-card">
						<view class="flex items-center justify-between w-full">
							<text class="load-title">负载</text>
							<text 
								class="load-status px-8 py-4 rd-4 text-24" 
								:class="loadChartStyle.statusClass"
							>
								{{ loadChartStyle.status }}
							</text>
						</view>
						<view class="load-chart-container" :style="{ background: mockServerStatus.status === false ? '#E7E7E7' : '#fff' }">
							<view 
								class="load-chart-fill"
								:style="{
									height: loadChartStyle.height,
									background: loadChartStyle.background
								}"
							>
								<text class="load-percentage">{{ loadChartStyle.usage }}%</text>
							</view>
						</view>
					</view>

					<!-- CPU指标 - 第2列第1行 -->
					<view class="metric-item cpu-item">
						<view class="flex flex-col items-center justify-between">
							<text class="metric-title">CPU</text>
							<text class="metric-subtitle px-8 py-4 rd-4 text-24">4核</text>
						</view>
						<ECharts
							canvas-id="cpu-chart"
							chart-type="gauge"
							:chart-data="getCpuChartData(mockServerStatus.status === false ? { usage: 0, cores: 4 } : { usage: 40, cores: 4 })"
							:height="110"
						/>
					</view>

					<!-- 内存指标 - 第3列第1行 -->
					<view class="metric-item memory-item">
						<view class="flex flex-col items-center justify-between">
							<text class="metric-title">内存</text>
							<text class="metric-subtitle px-8 py-4 rd-4 text-24">8GB</text>
						</view>
						<ECharts
							canvas-id="memory-chart"
							chart-type="gauge"
							:chart-data="getMemChartData(mockServerStatus.status === false ? { usage: 0, total: '8GB' } : { usage: 96, total: '8GB' })"
							:height="110"
						/>
					</view>

					<!-- 磁盘指标 - 第2-3列第2行 -->
					<view class="disk-item">
						<view class="disk-header mb-20">
							<text class="disk-title">磁盘(/)</text>
							<text 
								class="disk-status px-8 py-4 rd-4 text-24" 
								:class="diskChartStyle.statusClass"
							>
								{{ diskChartStyle.status }}
							</text>
						</view>
						<view class="disk-progress-container">
							<view class="disk-progress-bar" :style="{ background: mockServerStatus.status === false ? '#E7E7E7' : '#fff' }">
								<view 
									class="disk-progress-fill"
									:style="{
										width: diskChartStyle.width,
										background: diskChartStyle.background
									}"
								></view>
							</view>
							<text class="disk-percentage" :style="{ color: diskChartStyle.color }">{{ diskChartStyle.usage }}%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import { pageContainer, getCpuChartData, getMemChartData, getLoadChartStyle, getLoadStatusText, getDiskChartStyle, getDiskStatusText } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	// 【新增】模拟服务器状态数据（实际使用时从服务器数据中获取）
	const mockServerStatus = ref({
		status: false, // true: 在线, false: 离线, null: 加载中
		name: '宝塔Linux面板',
		ip: '*************',
		onlineTime: '在线2天'
	});

	// 【新增】模拟负载数据（实际使用时从服务器数据中获取）
	// 在实际项目中，这应该是：
	// const { serverList } = useServerListStore().getReactiveState();
	// const currentServer = serverList.value[0]; // 或从路由参数、props等获取当前服务器
	// const loadData = computed(() => currentServer?.load || { usage: 0, total: '连接中' });
	const mockLoadData = ref({
		usage: 70, // 使用率百分比
		total: '流畅'
	});

	// 【新增】模拟磁盘数据（实际使用时从服务器数据中获取）
	const mockDiskData = ref({
		usage: 48, // 使用率百分比
		total: '40GB'
	});

	// 【新增】计算负载图表样式
	const loadChartStyle = computed(() => {
		// 离线状态下显示0%
		if (mockServerStatus.value.status === false) {
			return getLoadChartStyle({ usage: 0, total: '离线' });
		}
		return getLoadChartStyle(mockLoadData.value);
	});

	// 【新增】计算磁盘图表样式
	const diskChartStyle = computed(() => {
		// 离线状态下显示0%
		if (mockServerStatus.value.status === false) {
			return getDiskChartStyle({ usage: 0, total: '离线' });
		}
		return getDiskChartStyle(mockDiskData.value);
	});

	// 【新增】模拟轮询更新负载数据（演示动态效果）
	// 在实际项目中，轮询由 useServerListPolling() 处理，无需手动模拟
	const simulateLoadUpdate = () => {
		const newLoadUsage = Math.floor(Math.random() * 100);
		mockLoadData.value = {
			usage: newLoadUsage,
			total: getLoadStatusText(newLoadUsage)
		};
		
		const newDiskUsage = Math.floor(Math.random() * 100);
		mockDiskData.value = {
			usage: newDiskUsage,
			total: getDiskStatusText(newDiskUsage)
		};
	};

	// 【演示用】定时更新 - 实际项目中请移除这部分代码
	// 实际项目中的数据更新流程：
	// 1. 页面加载时调用 getServerList() 获取基础数据
	// 2. 使用 useServerListPolling() 的 startPolling() 开始轮询
	// 3. 轮询会自动调用 getServerInfoList() 更新服务器状态
	// 4. 更新后的数据会通过响应式系统自动反映到UI上
	setInterval(simulateLoadUpdate, 3000); // 仅用于演示动态效果

	// 【演示用】切换服务器状态 - 用于测试离线样式效果
	const toggleServerStatus = () => {
		const statuses = [true, false, null]; // 在线、离线、加载中
		const currentIndex = statuses.indexOf(mockServerStatus.value.status);
		const nextIndex = (currentIndex + 1) % statuses.length;
		mockServerStatus.value.status = statuses[nextIndex];

		// 根据状态更新显示文本
		if (mockServerStatus.value.status === true) {
			mockServerStatus.value.onlineTime = '在线2天';
		} else if (mockServerStatus.value.status === false) {
			mockServerStatus.value.onlineTime = '离线';
		} else {
			mockServerStatus.value.onlineTime = '连接中';
		}
	};

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
		transition: all 0.3s ease;
	}

	/* 【新增】离线状态下的文字颜色调整 */
	.server-offline .text-30,
	.server-offline .text-24,
	.server-offline .metric-title,
	.server-offline .metric-subtitle,
	.server-offline .load-title,
	.server-offline .disk-title {
		color: #999999 !important;
	}

	/* 在线状态指示器 */
	.online-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #20a50a;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
		animation: ripple-online 2s infinite;
	}

	/* 【新增】离线状态指示器 */
	.offline-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #E7E7E7;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
	}

	/* 【新增】加载状态指示器 */
	.loading-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #999;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
		animation: pulse 1.5s infinite;
	}

	@keyframes ripple-online {
		0% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0.3);
		}
		70% {
			box-shadow: 0 0 0 12rpx rgba(32, 165, 10, 0);
		}
		100% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0);
		}
	}

	/* 【新增】加载状态的脉冲动画 */
	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(2, 1fr);
		gap: 8rpx;
		margin-top: 32rpx;
		height: 300rpx; /* 设置固定高度确保2行布局 */
	}

	/* 负载卡片 - 第1列，跨2行 */
	.load-card {
		grid-column: 1;
		grid-row: 1 / 3;
		background: rgba(247, 247, 247, 1);
		border-radius: 24rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		transition: all 0.3s ease;
	}

	.load-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		align-self: flex-start;
	}

	/* 【优化】负载状态样式 - 支持动态状态类 */
	.load-status {
		font-size: 24rpx;
		margin-bottom: 24rpx;
		align-self: flex-start;
		border-radius: 8rpx;
		transition: all 0.3s ease;
	}

	/* 【新增】负载状态的不同级别样式 */
	.load-status-normal {
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.load-status-medium {
		color: rgba(255, 193, 7, 1);
		background: rgba(255, 193, 7, 0.1);
	}

	.load-status-high {
		color: rgba(211, 47, 47, 1);
		background: rgba(211, 47, 47, 0.1);
	}

	/* 【优化】柱形图容器 */
	.load-chart-container {
		flex: 1;
		width: 100rpx;
		position: relative;
		background: #fff;
		border-radius: 16rpx;
		position: relative;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		overflow: hidden;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* 【优化】柱形图填充 - 支持动态高度和颜色，添加过渡动画 */
	.load-chart-fill {
		width: 100%;
		min-height: 5%; /* 最小高度保证可见性 */
		border-radius: 0 0 16rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		/* 【关键】CSS3过渡动画 - 支持高度和背景色的平滑过渡 */
		transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1), 
		           background 0.6s ease-in-out;
		/* 【新增】增加轻微的阴影效果，提升视觉层次 */
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	/* 【优化】负载百分比文字 - 添加动画效果 */
	.load-percentage {
		font-size: 28rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
		transition: transform 0.3s ease;
		position: absolute;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	/* 【新增】当负载值变化时的微动画效果 */
	.load-percentage:hover {
		transform: scale(1.05);
	}

	.metric-item {
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* CPU指标 - 第2列第1行 */
	.cpu-item {
		grid-column: 2;
		grid-row: 1;
	}

	/* 内存指标 - 第3列第1行 */
	.memory-item {
		grid-column: 3;
		grid-row: 1;
	}

	.metric-title {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 4rpx;
	}

	.metric-subtitle {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	/* 磁盘指标 - 第2-3列第2行 */
	.disk-item {
		grid-column: 2 / 4;
		grid-row: 2;
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 24rpx;
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.disk-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.disk-title {
		font-size: 24rpx;
		color: #333;
	}

	.disk-total {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	/* 【新增】磁盘状态的不同级别样式 */
	.disk-status {
		font-size: 20rpx;
		border-radius: 8rpx;
		transition: all 0.3s ease;
	}

	.disk-status-normal {
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.disk-status-medium {
		color: rgba(255, 193, 7, 1);
		background: rgba(255, 193, 7, 0.1);
	}

	.disk-status-high {
		color: rgba(255, 152, 0, 1);
		background: rgba(255, 152, 0, 0.1);
	}

	.disk-status-critical {
		color: rgba(211, 47, 47, 1);
		background: rgba(211, 47, 47, 0.1);
	}

	.disk-progress-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.disk-progress-bar {
		flex: 1;
		height: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.disk-progress-fill {
		height: 100%;
		border-radius: 8rpx;
		/* 【关键】CSS3过渡动画 - 支持宽度和背景色的平滑过渡 */
		transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1), 
		           background 0.6s ease-in-out;
		min-width: 5%; /* 最小宽度保证可见性 */
		/* 【新增】增加轻微的阴影效果，提升视觉层次 */
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);
	}

	.disk-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
		/* 【新增】当磁盘值变化时的微动画效果 */
		transition: color 0.3s ease;
	}
</style>
